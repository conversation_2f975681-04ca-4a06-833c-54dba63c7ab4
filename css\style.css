@import url('https://fonts.cdnfonts.com/css/poppins');

body {
    font-family: 'Poppins', sans-serif;
}

.text-primary {
    color: rgba(25, 77, 255, 1) !important;
}

.circle-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    padding: 8px;
    border-radius: 50%;
    background-color: #194DFF;
    color: #f0f0f0;
    font-size: 20px;
}

.navbar {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar .navbar-brand img {
    width: 50px;
}

.navbar .navbar-nav .nav-link {
    margin: 0 25px 0 0;
}

.navbar .navbar-nav .nav-link {
    color: rgba(139, 140, 141, 1);
    font-weight: 300;
}

.navbar .navbar-nav .nav-link.active {
    background: rgba(25, 77, 255, 1) !important;
    color: #fff;
    border-radius: 12px;
    padding: 12px 16px;
}

header {
    padding-top: 9px;
    position: relative;
    z-index: 5;
    background-color: #FCFCFD;
}

/* header::before {
    width: 800px;
    height: 800px;
    background: radial-gradient(50% 50% at 50% 50%, rgba(25, 77, 255, 0.25) 0%, rgba(25, 77, 255, 0) 100%);
    position: absolute;
    z-index: 0;
    content: '';
    top: -10rem;
    left: -10rem;
    opacity: .5;
} */

header h1 {
    font-size: 56px;
    font-weight: 500;
    margin-bottom: 10px;
}

header p {
    color: rgba(139, 140, 141, 1);
    font-weight: 300;
    margin-bottom: 24px;
}

header a {
    text-decoration: none;
    color: rgba(139, 140, 141, 1) !important;
}

header a i {
    margin-left: 8px;
    font-size: 14px;
}

header .clients {
    margin-top: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

header .clients .clients-item {
    align-items: center;
    margin-right: 24px;
    justify-content: center;
}

header .clients .clients-item img {
    width: 40px;
}

header .clients .clients-item h3 {
    font-size: 14px;
    color: rgba(139, 140, 141, 1);
    margin-bottom: 0;
    font-weight: 500;
    margin-left: 12px;
}

header h6 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 0;
    text-align: center;
    margin-top: 28px;
}

header .img-header {
    width: 100%;
    object-fit: cover;
    aspect-ratio: 9/10;
}

.btn {
    padding: 16px 24px;
    border-radius: 12px;
}

.btn.btn-primary {
    background: linear-gradient(0deg, #194DFF, #194DFF), linear-gradient(0deg, #194DFF, #194DFF);
    border-radius: 12px;
}

.clients-slide {
    text-align: right;
    margin-top: 2rem;
}

.clients-slide a,
.school-home-slide a,
.testimonials-slide a {
    background: rgba(241, 241, 241, 1);
    padding: 10px 17px;
    border-radius: 10px;
    margin-right: 5px;
    text-decoration: none;
}

.clients-slide a i,
.school-home-slide a i,
.testimonials-slide a i {
    margin: 0 !important;
    color: rgba(139, 140, 141, 1);
}

.clients-slide a.active,
.school-home-slide a.active,
.testimonials-slide a.active {
    background: linear-gradient(0deg, #194DFF, #194DFF),
        linear-gradient(0deg, #194DFF, #194DFF);
}

.clients-slide a.active i,
.school-home-slide a.active i,
.testimonials-slide a.active i {
    color: #fff;
}

section {
    padding-top: 100px;
}

.pb-100 {
    padding-bottom: 100px;
}

.bg-grey {
    background-color: rgba(249, 250, 251, 1);
}

header .label.label-primary {
    margin-top: 80px;
    margin-bottom: 40px;
}

header .label.label-primary,
section.features .label.label-primary,
section.about .label.label-primary,
section.testimonial .label.label-primary,
section.contact .label.label-primary {
    background: rgba(232, 237, 255, 1);
    border: 1px solid rgba(149, 173, 255, 1);
    display: inline-block;
    padding: 8px 16px;
    color: rgba(25, 77, 255, 1);
    border-radius: 16px;
    font-size: 14px;
}

header .label.label-primary img,
section.features .label.label-primary img,
section.about .label.label-primary img,
section.testimonial .label.label-primary img,
section.contact .label.label-primary img {
    margin-right: 4px;
}

section.features h2,
section.about h2,
section.user,
section.testimonial h2 {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #101828;
}

section.features p.subtitle,
section.user p.subtitle,
section.about p.subtitle {
    max-width: 800px;
    margin: auto;
    color: #8B8C8D;
    font-weight: 400;
    font-size: 14px;
}

section.services h2 b,
section.features h2 b,
section.user h2 b {
    color: #194DFF;
    font-weight: 600;
}

section.features p {
    color: #8B8C8D;
    font-weight: 400;
    font-size: 14px;
}

section.features {
    position: relative;
    overflow: hidden;
}

section.features::before {
    width: 800px;
    height: 800px;
    position: absolute;
    z-index: 0;
    content: '';
    top: 0;
    right: -10rem;
    opacity: .5;
}

section.features .features {
    margin-top: 48px;
}

section.features .features .features-item {
    display: flex;
    border-bottom: 1px solid #F2F4F7;
    margin-top: 16px;
}

section.features .features .features-item .features-number {
    color: rgba(46, 47, 47, 1);
    font-weight: 600;
    font-size: 18px;
    border: 1px solid rgba(232, 237, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    padding: 16px;
}

section.features .features .features-item h3 {
    color: #2E2F2F;
    font-weight: 500;
    font-size: 18px;
    margin-left: 10px;
}

section.features .features .features-item p {
    font-size: 14px;
    color: #8B8C8D;
    font-weight: 400;
    margin-left: 10px;
}

/* ABOUT */

section.about {
    padding-top: 100px;
}

section.about .label.label-primary {
    margin-bottom: 20px;
}

/* USER */
section.user {
    padding-top: 100px;
    text-align: center;
}

section.user .user-item {
    margin-top: 32px;
    background-color: #F9FAFB;
    border-radius: 16px;
    padding: 24px;
}

section.user .user-item img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-bottom: 24px;
}

section.user .user-item h3 {
    font-size: 40px;
    font-weight: 500;
    color: #000000;
}

section.user .user-item p {
    font-size: 14px;
    color: #475467;
    font-weight: 400;
}

/* PORTFOLIO */
section.portfolio {
    padding-top: 100px;
}

section.portfolio .portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

section.portfolio .portfolio-header h2 {
    font-size: 32px;
    font-weight: 500;
    color: #101828;
    margin: 0;
}

section.portfolio .portfolio-header h2 b {
    color: #194DFF;
    font-weight: 600;
}

section.portfolio .portfolio-header ul li .nav-link {
    font-size: 16px;
    font-weight: 500;
    color: #8B8C8D;
}

section.portfolio .portfolio-header ul li .nav-link.active {
    color: #101828;
    font-weight: 600;
}

section.portfolio .portfolio-groupitem {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    flex-wrap: wrap;
    gap: 24px;
}

section.portfolio .portfolio-groupitem>* {
    flex: 0 0 calc(50% - 12px);
}

section.portfolio .portfolio-item {
    margin-top: 24px;
}

section.portfolio .portfolio-item img {
    width: 100%;
    object-fit: cover;
    aspect-ratio: 9/9;
}

section.portfolio .portfolio-item.active img {
    width: 100%;
    aspect-ratio: 9/16;
    height: unset;
}

section.portfolio .portfolio-item h3 {
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    margin-top: 24px;
}

section.portfolio .portfolio-item p {
    font-size: 14px;
    color: #475467;
    font-weight: 400;
}

/* TESTIMONI */

section.testimonial hr {
    border: 1px solid rgba(241, 241, 241, 1);
}

section.testimonial .testimonials {
    margin-top: 24px;
}

section.testimonial p {
    font-weight: 400;
    color: rgba(139, 140, 141, 1);
    margin-bottom: 0;
    font-size: 14px;
}

section.testimonial p.span {
    color: rgba(25, 77, 255, 1);
}

/* BONUS */

section.bonus {
    padding-top: 100px;
}

section.bonus .bonus-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

section.bonus .bonus-header h2 {
    font-size: 32px;
    font-weight: 500;
    color: #101828;
    margin: 0;
    width: 100%;
}

section.bonus .bonus-header h2 b {
    color: #194DFF;
    font-weight: 600;
    margin: 0;
}

section.bonus .bonus-header p {
    font-size: 14px;
    color: #8B8C8D;
    font-weight: 400;
    margin: 0;
}

section.bonus .bonus-img img {
    margin-top: 24px;
    width: 100%;
}

section.bonus .bonus-item {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 24px;
    border-radius: 16px;
    background-color: #F9FAFB;
    gap: 8px;
    height: 100%;
}

section.bonus .bonus-item img {
    width: 24px;
    height: 24px;
}

section.bonus .bonus-item p {
    font-size: 16px;
    color: #101828;
    font-weight: 400;
    margin: 0;
}

.card {
    border: 1px solid rgba(234, 236, 240, 1);
    border-radius: 12px;
    padding: 30px;
}

.card .card-body {
    min-height: 250px;
}

.card .card-footer {
    border: none;
}

.card h2 {
    font-size: 22px;
    margin-bottom: 0;
    font-weight: 600;
}

.card p {
    font-weight: 400;
}

section.testimonial .testimonials .card {
    padding: 48px 24px;
    border: 1px solid rgba(232, 237, 255, 1);
    border-radius: 16px;
}

section.testimonial .testimonials .card .card-body .testimonials-detail {
    display: flex;
}

section.testimonial .testimonials .card .card-body .testimonials-detail img {
    width: 65px;
    margin-bottom: 24px;
    margin-right: 22px;
}

section.testimonial .testimonials .card .card-body .testimonials-detail h3 {
    font-size: 20px;
    font-weight: 600;
    color: rgba(46, 47, 47, 1);
}

section.testimonial .testimonials .card .card-body .testimonials-detail p {
    font-size: 14px;
    color: rgba(139, 140, 141, 1);
    font-weight: 300;
}

section.testimonial .testimonials .card .card-body .testimonials-detail .btn {
    padding: 8px 12px;
    border: 1px solid rgba(232, 237, 255, 1);
    border-radius: 16px;
    color: rgba(25, 77, 255, 1);
    font-size: 14px;
}

section.testimonial .testimonials .card .card-body .testimonials-detail .btn:hover {
    color: #fff;
}

section.ready {
    padding-bottom: 100px;
}

section.ready .ready-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.container {
    position: relative;
}

section.ready .container {
    background-image: url(../img/svg/ready.svg);
    background-size: cover;
    padding: 60px 50px;
    color: #fff;
    border-radius: 20px;
}

section.ready p {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 300;
}

section.ready h3 {
    font-size: 26px;
}

footer {
    margin-top: 3rem;
    padding-bottom: 100px;
}

footer img {
    width: 50px;
}

footer span {
    font-weight: 600;
    font-size: 16px;
}

footer hr {
    border: 1px solid rgba(241, 241, 241, 1)
}

footer p {
    font-size: 14px;
    color: rgba(139, 140, 141, 1);
    font-weight: 400;
    margin: 0;
}

footer a {
    text-decoration: none;
    font-size: 12px;
    color: #8B8C8D;
    font-weight: 500;
    margin: 0;
}

footer .footer-sitemap a {
    margin-left: 20px;
}

footer .footer-sitemap a:not(:last-child) {
    border-right: 1px solid rgba(241, 241, 241, 1);
    padding-right: 20px;
}

.windown-width-min-992 {
    display: block !important;
}

.window-width-max-1400 {
    display: none !important;
}

.testimonials-item {
    padding: 12px;
}

.testimonials .slick-slide {
    height: unset;
}

.faq {
    margin-top: 100px;
}

.faq p {
    font-weight: 400;
    color: #667085;
    font-size: 14px;
}

.faq a {
    text-decoration: none;
    color: #101828;
    font-weight: 500;
    font-size: 24px;
}

.faq .faq-item {
    padding-bottom: 30px;
    padding-top: 30px;
    padding: 30px;
}

.faq .faq-item:first-child {
    padding-top: 0;
}

.faq .faq-item a i {
    transition: all 0.3s;
}

.faq .faq-item a[aria-expanded="true"] {
    color: #194DFF;
}

.faq .faq-item a[aria-expanded="true"] i.fa-plus {
    display: none;
}

.faq .faq-item a[aria-expanded="true"] i.fa-minus {
    display: block;
}

.faq .faq-item a[aria-expanded="false"] i.fa-plus {
    display: block;
}

.faq .faq-item a[aria-expanded="false"] i.fa-minus {
    display: none;
}

.faq .faq-item h1 {
    font-size: 24px;
    font-weight: 500;
    color: #101828;
    width: 80%;
}

.faq .faq-item a[aria-expanded="true"] h1 {
    color: #194DFF;
}

.faq .card {
    background-color: #F9FAFB;
}

.faq .card .faq-item:has(.collapse.show) {
    background-color: #FFFFFF;
}

.school-home {
    padding-top: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.school-home-list {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    border-radius: 12px;
    width: 80%;
}

.school-home-list .school-home-item {
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.school-home-list .school-home-item img {
    width: 40px;
    height: 40px;
}

.school-home-list .school-home-item h3 {
    color: #8B8C8D;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

/* SERVICES */
section.services {
    padding-top: 100px;
}

section.services h2 {
    font-size: 32px;
    font-weight: 500;
    text-align: center;
    color: #101828;
}

section.services .services-item {
    padding: 20px;
    border: 1px solid #F2F4F7;
    border-radius: 18px;
    background-color: #FCFCFD;
    height: 100%;
    margin-top: 24px;
}

section.services .services-item h3 {
    font-size: 23px;
    font-weight: 500;
    color: #2E2F2F;
    margin-top: 50px;
}

section.services .services-item p {
    font-size: 14px;
    font-weight: 400;
    color: #8B8C8D;
}

section.services .services-item a {
    text-decoration: none;
    color: #101828;
    font-weight: 500;
    text-align: end;
}

section.services .services-item a i {
    margin-left: 8px;
    background-color: unset;
    color: #101828;
}

section.services .services-item .services-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 85%;
}

/* ABSENSI DIGITAL */
header.absensi-digital {
    padding-top: 100px;
    background-color: unset;
}

header.absensi-digital h1 {
    font-size: 48px;
    font-weight: 500;
}

header.absensi-digital span {
    color: #194DFF;
}

header.absensi-digital .video img {
    width: 100%;
    border-radius: 12px;
    margin-top: 24px;
}

header.absensi-digital .video iframe {
    width: 100%;
    border-radius: 12px;
    height: 605px;
    margin-top: 24px;
}

section.list-absensi {
    padding-top: 5px;
}

section.list-absensi .list-absensi-item {
    padding: 24px;
    border: 1px solid #F9FAFB;
    border-radius: 16px;
    background-color: #F9FAFB;
    height: 100%;
    text-align: center;
}

section.list-absensi .list-absensi-item img {
    width: 68px;
    height: 68px;
}

section.list-absensi .list-absensi-item h2 {
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    margin-top: 10px;
}

section.list-absensi .list-absensi-item p {
    font-size: 14px;
    font-weight: 400;
    color: #475467;
}

section.list-absensi .list-absensi-item a {
    text-decoration: none;
    color: #101828;
    font-weight: 500;
    font-size: 12px;
    margin-top: 10px;
}

section.school-absensi {
    font-size: 32px;
    font-weight: 500;
    color: #101828;
}

section.school-absensi h1 {
    font-size: 32px;
    font-weight: 500;
    color: #101828;
}

section.school-absensi p {
    font-size: 14px;
    font-weight: 400;
    color: #8B8C8D;
}

section.school-absensi .school-absensi-item {
    padding: 24px;
    border: 1px solid #EAECF0;
    border-radius: 12px;
    background-color: #FFFFFF;
    height: 100%;
    margin-top: 24px;
    text-align: center;
}

section.school-absensi .school-absensi-item img {
    width: 111px;
    height: 106px;
}

section.testimonial .testimonial-author {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

section.testimonial .testimonial-author .testimonial-author-image img {
    width: 40px;
    height: 40px;
}

section.testimonial .testimonial-author h3 {
    font-size: 14px;
    font-weight: 400;
    color: #101828;
}

section.testimonial .testimonials-item.testimonials-absensi .card-body {
    min-height: unset;
}

/* BLOG */
header.blog {
    padding-top: 100px;
    background-color: unset;
}

header.blog .input-group .form-control {
    border-radius: 999px;
    padding: 14px;
    background-color: #F9FAFB;
    border: 1px solid #EAECF0;
    border-left: none;
    color: #344054;
    font-size: 18px;
    font-weight: 400;
}

header.blog .input-group .form-control::placeholder {
    color: #344054;
    font-size: 18px;
    font-weight: 400;
}

header.blog .input-group .input-group-text {
    border-radius: 999px;
    padding: 14px;
    background-color: #F9FAFB;
    border: 1px solid #EAECF0;
    border-right: none;
    color: #0054F0;
}

section.blog {
    padding-top: unset;
}

section.blog .blog-item img {
    width: 100%;
    height: 408px;
    border-radius: 12px;
}

section.blog .blog-item .blog-thumbnail {
    position: relative;
    display: inline-block;
}

section.blog .blog-item .blog-thumbnail img {
    width: 100%;
    display: block;
}

section.blog .blog-item .group-button {
    position: absolute;
    bottom: -1px;
    right: 0px;
    background-color: #fff;
    padding: 10px;
    border-radius: 32px 0 24px 0;
}

section.blog .blog-item .group-button-icon a {
    background-color: #000000;
    border: none;
    padding: 16px;
    border-radius: 50%;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

section.blog .blog-item .group-button-icon a i {
    color: white;
    rotate: 320deg;
}

section.blog .blog-item .group-button-icon a:hover {
    background-color: #194DFF;
}

section.blog .blog-item h3 {
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    margin-top: 24px;
}

section.blog .blog-item .blog-date i {
    color: #194DFF;
}

section.blog .blog-item .blog-date span {
    color: #344054;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
}

section.blog-populer {
    padding-top: 50px;
}

section.blog-populer .blog-populer-item {
    display: flex;
    gap: 16px;
    width: 95%;
}

section.blog-populer .blog-populer-item img {
    width: 150px;
    height: 150px;
    border-radius: 12px;
    object-fit: cover;
}

section.blog-populer .blog-populer-item .blog-populer-date i {
    color: #194DFF;
}

section.blog-populer .blog-populer-item .blog-populer-date span {
    color: #344054;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
}

section.blog-populer .blog-populer-item h3 {
    font-size: 20px;
    font-weight: 500;
    color: #000000;
    margin-top: 10px;
}

section.blog-populer .blog-populer-item p {
    font-size: 14px;
    font-weight: 400;
    color: #475467;
}

.paginationjs-bootstrap5 .pagination {
    display: flex;
    justify-content: center;
    padding: 10px 0;
}

.paginationjs-bootstrap5 .pagination li {
    margin: 0 5px;
    list-style: none;
}

.paginationjs-bootstrap5 .pagination li a {
    padding: 6px 12px;
    border-radius: 5px;
    text-decoration: none;
    background-color: #FFFFFF;
    border: 1px solid #EAECF0;
    color: #007bff;
    transition: 0.3s;
}

.paginationjs-bootstrap5 .pagination li.active a,
.paginationjs-bootstrap5 .pagination li a:hover {
    background-color: #0054F0;
    color: white;
    height: 42px;
}

.paginationjs .paginationjs-pages li {
    float: left;
    border: unset;
    border-right: none;
    list-style: none;
}

.paginationjs .paginationjs-pages li>a {
    min-width: 30px;
    padding: 6px 12px;
    height: 42px;
    line-height: 28px;
    display: block;
    background: #FFFFFF;
    font-size: 14px;
    color: #344054;
    text-decoration: none;
    text-align: center;
}

.paginationjs .paginationjs-pages li:last-child {
    border-right: unset;
    border-radius: 0 3px 3px 0;
}

/* BLOG DETAIL */
header.blog-detail {
    padding-top: 100px;
    background-color: unset;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

header.blog-detail p {
    font-size: 18px;
    font-weight: 400;
    color: #667085;
    margin-bottom: 10px;
}

header.blog-detail h1 {
    font-size: 40px;
    font-weight: 500;
    color: #000000;
}

header.blog-detail h2 {
    font-size: 18px;
    font-weight: 400;
    color: #194DFF;
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

header.blog-detail h2 span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667085;
}

header.blog-detail h2 i {
    color: #667085;
    font-size: 8px;
}

section.blog-detail {
    padding-top: 10px;
}

section.blog-detail .blog-detail-img {
    width: 100%;
    border-radius: 12px;
    aspect-ratio: 16/7;
    object-fit: cover;
    margin-bottom: 32px;
}

section.blog-detail .blog-detail-content {
    width: 80%;
    margin: auto;
}

section.blog-detail .blog-detail-content p {
    font-size: 16px;
    font-weight: 400;
    color: #475467;
}

section.blog-detail .blog-detail-share {
    text-align: center;
}

section.blog-detail .blog-detail-share .group-medsos {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 10px;
}

section.blog-detail .blog-detail-share .group-medsos .medsos-icon {
    background-color: #F9FAFB;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}

section.blog-detail .blog-detail-share .group-medsos .medsos-icon:hover {
    background-color: #F9FAFB;
}

section.blog .blog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

section.blog .blog-header h1 {
    font-size: 32px;
    font-weight: 500;
    color: #000000;
    margin: 0;
}

section.blog .blog-header a {
    text-decoration: none;
    color: #194DFF;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
}

section.blog .blog-header .circle-icon {
    background-color: #F9FAFB;
    color: #194DFF;
}

section.blog .blog-header .circle-icon.active {
    background-color: #194DFF;
}

section.blog .blog-header .circle-icon.active i {
    color: #fff;
}

/* KONTAK KAMI */
header.contact {
    background-color: unset;
    text-align: center;
}

header.contact h2 {
    font-size: 32px;
    font-weight: 500;
    color: #101828;
}

header.contact p {
    font-size: 14px;
    font-weight: 400;
    color: #8B8C8D;
    width: 60%;
    margin: auto;
}

section.contact .contact-info {
    display: flex;
    align-items: center;
    gap: 24px;
    background-color: #FFFFFF;
    border: 1px solid #EAECF0;
    padding: 12px 24px;
    border-radius: 12px;
}

section.contact .contact-info img {
    width: 20px;
    height: 20px;
}

section.contact .contact-info h3 {
    font-size: 14px;
    font-weight: 400;
    color: #344054;
    margin: 0;
}

section.contact .contact-info p {
    font-size: 16px;
    font-weight: 500;
    color: #101828;
    margin: 0;
}

section.contact label {
    font-size: 14px;
    font-weight: 400;
    color: #101828;
}

section.contact .input-group .form-control {
    border: 1px solid #EAECF0;
    background-color: #F9FAFB;
    padding: 24px 4px;
    border-left: none;
    border-radius: 24px;
}

section.contact .input-group .form-control::placeholder {
    font-size: 14px;
    font-weight: 400;
    color: #344054;
}

section.contact .input-group .form-control:focus {
    border: 1px solid #194DFF;
}

section.contact .input-group .input-group-text {
    border: 1px solid #EAECF0;
    background-color: #F9FAFB;
    border: 1px solid #EAECF0;
    border-right: none;
    color: #0054F0;
    border-radius: 24px;
    padding: 8px 16px;
}

section.contact .contact-sosmed {
    display: flex;
    gap: 8px;
    margin-top: 24px;
}

section.contact .contact-sosmed a {
    text-decoration: none;
}

section.contact .contact-sosmed .medsos-icon {
    border: 1px solid #F2F4F7;
    background-color: #E6EEFE;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}

section.contact .contact-info-flex {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 24px;
    height: 100%;
}

section.faq.all {
    padding-top: 40px;
    margin-top: unset;
}

section.blog .blog-item .blog-thumbnail-slide {
    width: 95%;
}

.margin-start {
    margin-left: 24px;
}

.res-lg {
    display: flex;
}

.res-md {
    display: none;
}

.features-lg {
    display: block;
}

.features-sm {
    display: none;
}

.about-lg {
    display: block;
}

.about-sm {
    display: none;
}

footer .header-footer {
    display: flex;
    justify-content: space-between;
}

footer .footer-sitemap {
    display: flex;
    align-items: center;
}

footer .footer-medsos {
    display: flex;
}

footer .footer-medsos .medsos-icon {
    background-color: #FFFFFF;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
    border: 1px solid #EAECF0;
    color: #194DFF;
}

footer .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
}

.features-img-lg {
    display: block;
}

.features-img-sm {
    display: none;
}

@media screen and (max-width: 1399px) {

    header h1 {
        font-size: 38px;
    }

    .faq .faq-item h1 {
        line-height: 1.5;
    }
}

@media screen and (max-width: 1199px) {

    header h1 {
        font-size: 28px;
    }

    header p {
        font-size: 14px;
    }

    header .btn {
        padding: 12px 20px;
    }

    .window-width-max-1400 {
        display: block !important;
    }

    section.features h2,
    section.testimonial h2 {
        font-size: 26px;
    }

    section.features p {
        font-size: 14px;
    }

    section.services h2 {
        font-size: 28px;
    }

    section.services {
        padding-top: 50px;
    }

    section.services .services-item h3 {
        margin-top: 24px;
        font-size: 16px;
    }

    section.services .services-item p {
        font-size: 12px;
    }

    section.services .services-item a {
        font-size: 14px;
    }

    .circle-icon {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }

    section.features h2,
    section.testimonial h2 {
        font-size: 20px;
    }

    section.features .features {
        margin-top: 20px;
    }

    section.features .features .features-item h3 {
        font-size: 16px;
    }

    section.features .features .features-item p {
        font-size: 12px;
    }

    .school-home-list {
        width: 75%;
    }

    section.features img {
        aspect-ratio: 9/12;
        object-fit: cover;
    }

    section.about {
        padding-top: 80px;
    }

    section.features h2,
    section.about h2,
    section.user h2,
    section.testimonial h2 {
        font-size: 28px;
    }

    section.about img {
        aspect-ratio: 10/11;
        object-fit: cover;
        border-radius: 12px;
    }

    section.user .user-item h3 {
        font-size: 32px;
    }

    section.portfolio .portfolio-header h2 {
        font-size: 28px;
    }

    section.portfolio .portfolio-header ul li .nav-link {
        font-size: 14px;
    }

    section.portfolio .portfolio-groupitem {
        margin-top: unset;
        gap: unset;
    }

    section.portfolio .portfolio-item {
        margin-top: unset;
    }

    section.bonus .bonus-header h2 {
        font-size: 28px;
    }

    .faq .faq-item h1 {
        font-size: 20px;
        line-height: 1.5;
    }

    section.ready h3 {
        font-size: 20px;
    }

    header.absensi-digital h1 {
        font-size: 42px;
    }

    section.school-absensi h1 {
        font-size: 28px;
    }

    section.school-absensi p {
        font-size: 12px;
    }

    section.testimonial .testimonial-author h3 {
        line-height: 1.5;
    }

    section.testimonial p {
        font-size: 12px;
        line-height: 1.5;
    }

    .faq a {
        font-size: 20px;
    }

    section.blog .blog-item h3 {
        font-size: 20px;
    }

    section.blog .blog-item img {
        height: unset;
        aspect-ratio: 9/12;
        object-fit: cover;
    }

    section.blog .blog-item .group-button {
        border-radius: 32px 0 10px 0;
    }

    section.blog-populer h1 {
        font-size: 28px;
    }

    section.blog-populer .blog-populer-item .blog-populer-date span {
        font-size: 14px;
    }

    section.blog-populer .blog-populer-item h3 {
        font-size: 18px;
    }

    section.contact .contact-sosmed {
        justify-content: center;
        margin-bottom: 32px;
    }

    header.contact h2 {
        font-size: 28px;
    }

    footer .footer-sitemap a:not(:last-child) {
        padding-right: 12px;
    }

    footer .footer-sitemap a {
        margin-left: 12px;
    }
}

@media screen and (max-width: 992px) {

    /* header {
        text-align: center;
    } */

    header.home {
        padding-top: 100px;
    }

    header .label.label-primary {
        margin-top: 24px;
        margin-bottom: 24px;
    }

    .school-home-list {
        width: 85%;
    }

    .school-home {
        padding-top: 24px;
    }

    header .school-home.res-lg {
        display: none;
    }

    header .school-home.res-md {
        display: flex;
    }

    header .img-header {
        margin-top: 24px;
        aspect-ratio: 9/12;
    }

    section.services {
        padding-top: 90px;
    }

    section.services .services-item a {
        font-size: 13px;
    }

    .circle-icon {
        font-size: 14px;
    }

    section.services h2 {
        font-size: 24px;
    }

    section.features img {
        aspect-ratio: 16/14;
        border-radius: 12px;
    }

    section.features {
        padding-top: 70px;
    }

    section.features h2 {
        margin-top: 24px;
    }

    section.about {
        padding-top: 50px;
    }

    .margin-start {
        margin-left: unset;
    }

    section.about img {
        aspect-ratio: 10/19;
    }

    section.features h2,
    section.about h2,
    section.user h2,
    section.testimonial h2 {
        font-size: 24px;
    }

    section.user .user-item h3 {
        font-size: 28px;
    }

    section.portfolio {
        padding-top: 50px;
    }

    section.portfolio .portfolio-header h2 {
        font-size: 20px;
    }

    section.portfolio .portfolio-header ul li .nav-link {
        font-size: 12px;
    }

    section.portfolio .portfolio-item.active img {
        aspect-ratio: 6/11;
    }

    section.bonus .bonus-header h2 {
        font-size: 24px;
    }

    section.bonus .bonus-header p {
        font-size: 13px;
    }

    .faq {
        margin-top: 60px;
    }

    section.ready .ready-content {
        flex-direction: column;
    }

    section.ready .ready-content .btn {
        margin-top: 24px;
    }

    footer span {
        font-size: 12px;
    }

    footer .footer-medsos .medsos-icon {
        width: 30px;
        height: 30px;
        border-radius: 8px;
    }

    footer .footer-sitemap a:not(:last-child) {
        padding-right: 6px;
    }

    footer .footer-sitemap a {
        margin-left: 6px;
    }

    footer .footer-logo img {
        width: 40px;
        height: 40px;
    }

    footer p {
        font-size: 12px;
        color: rgba(139, 140, 141, 1);
        font-weight: 400;
        margin: 0;
    }

    header.absensi-digital .video img {
        height: unset;
    }

    section.school-absensi h1 {
        font-size: 24px;
    }

    section.features img {
        aspect-ratio: 8/6;
    }

    section.school-absensi {
        padding-top: 50px;
    }

    .row-cols-5>* {
        width: 33%;
        margin-top: 10px;
    }

    section.blog .blog-item h3 {
        font-size: 16px;
    }

    section.blog .blog-item .blog-date span {
        font-size: 14px;
    }

    header.blog-detail h1 {
        font-size: 32px;
    }

    header.blog-detail h2 {
        font-size: 16px;
    }

    section.blog-detail {
        padding-top: 0px;
    }

    section.blog-detail .blog-detail-content {
        width: 90%;
    }

    section.blog .blog-header h1 {
        font-size: 24px;
    }

    header.contact {
        padding-top: 50px;
    }

    section.contact {
        padding-top: 60px;
    }
}

@media screen and (max-width: 768px) {
    header .img-header {
        margin-top: 24px;
        aspect-ratio: 16/9;
    }

    .school-home {
        margin-top: unset;
        padding-top: 56px;
        display: unset;
        justify-content: column;
        align-items: center;
    }

    .school-home-list {
        width: 100%;
    }

    .school-home-slide {
        display: none;
    }

    .school-home-list {
        margin-top: 20px;
    }

    .features-lg {
        display: none;
    }

    .features-sm {
        display: block;
        margin-top: 24px;
    }

    section.features img {
        aspect-ratio: 16/9;
    }

    .about-lg {
        display: none;
    }

    .about-sm {
        display: block;
    }

    section.about img {
        aspect-ratio: 16/9;
    }

    section.about .label.label-primary {
        margin-top: 24px;
    }

    section.portfolio .portfolio-header {
        display: unset;
    }

    section.portfolio .portfolio-header .nav {
        justify-content: center !important;
        margin-top: 12px;
    }

    section.portfolio .portfolio-item.active img {
        aspect-ratio: 9/16;
        margin-bottom: 24px;
    }

    section.bonus .bonus-header {
        display: unset;
    }

    section.bonus .bonus-img img {
        aspect-ratio: 16/9;
        object-fit: cover;
        border-radius: 12px;
    }

    section.ready .ready-content {
        display: flex;
        justify-content: space-between;
        align-items: unset;
    }

    .header-footer {
        flex-direction: column;
        align-items: center;
    }

    .header-footer .footer-logo {
        margin-bottom: 24px;
    }

    .footer-sitemap {
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-sitemap a {
        margin-left: 0;
        margin-bottom: 18px;
    }

    .footer-bottom {
        flex-direction: column;
        align-items: center;
    }

    .footer-bottom a {
        margin: 0;
    }

    footer .footer-bottom {
        margin-top: unset;
    }

    footer span {
        font-size: 16px;
    }

    footer p {
        font-size: 14px;
        margin: unset;
    }

    .faq .faq-item h1 {
        font-size: 16px;
        line-height: 1.5;
    }

    section.blog .blog-item img {
        height: unset;
        aspect-ratio: 16/9;
        object-fit: cover;
    }
}

@media screen and (max-width: 576px) {

    .btn {
        font-size: 14px
    }

    header .label.label-primary,
    section.features .label.label-primary,
    section.about .label.label-primary,
    section.testimonial .label.label-primary,
    section.contact .label.label-primary {
        font-size: 12px;
    }

    header.home {
        text-align: center;
    }

    header h1 {
        font-size: 32px;
    }

    header p {
        font-size: 14px;
    }

    header .img-header {
        aspect-ratio: 16/16;
    }

    .school-home-list .school-home-item img {
        width: 32px;
        height: 32px;
    }

    .school-home {
        padding-top: 10px;
    }

    section.services h2 {
        font-size: 20px;
    }

    section.services {
        padding-top: 60px;
    }

    section.services .services-item h3 {
        font-size: 18px;
    }

    section.services .services-item p {
        font-size: 14px;
    }

    section.services .services-item a {
        font-size: 12px;
    }

    section.features h2,
    section.about h2,
    section.user h2,
    section.testimonial h2 {
        font-size: 20px;
        text-align: center;
    }

    .testimonials-footer {
        flex-direction: column;
    }

    .testimonials-footer .d-flex {
        margin-top: 24px;
    }

    section.ready .container {
        border-radius: 0;
    }

    .testimonials-detail {
        flex-direction: column;
    }

    .testimonials-detail .testimonial-image {}

    section.portfolio .portfolio-groupitem {
        flex-direction: column;
    }

    section.ready .container {
        padding: 60px 20px;
    }

    footer .footer-sitemap {
        flex-direction: column;
    }

    footer .footer-sitemap a:not(:last-child) {
        border-right: unset;
    }

    footer .footer-bottom {
        flex-direction: column;
    }

    footer .footer-bottom .footer-sitemap {
        flex-direction: unset;
        justify-content: unset;
        margin-top: 10px;
    }

    section.features h2,
    section.about h2,
    section.user h2,
    section.testimonial h2 {
        font-size: 20px;
    }

    section.features p.subtitle,
    section.user p.subtitle,
    section.about p.subtitle {
        line-height: 1.5;
        text-align: justify;
    }

    section.about {
        text-align: center;
    }

    section.user {
        padding-top: 60px;
    }

    section.portfolio .portfolio-header h2 {
        text-align: center;
    }

    section.portfolio .portfolio-header .nav {
        display: none;
    }

    section.testimonial .testimonials {
        margin-top: unset;
    }

    section.testimonial .testimonials .card .card-body .testimonials-detail h3 {
        font-size: 14px;
    }

    section.testimonial .testimonials .card .card-body .testimonials-detail p {
        font-size: 12px;
    }

    section.testimonial .testimonials .card .card-body .testimonials-detail .btn {
        font-size: 12px;
    }

    section.testimonial .testimonials .card .card-body .testimonials-detail img {
        width: 55px;
        margin-right: unset;
    }

    section.bonus {
        text-align: center;
    }

    section.bonus .bonus-header h2 {
        font-size: 20px;
    }

    section.bonus .bonus-header p {
        font-size: 14px;
    }

    section.bonus .bonus-img.lg {
        display: none;
    }

    .faq .faq-item h1 {
        font-size: 18px;
        line-height: 1.6;
    }

    section.ready h3 {
        font-size: 18px;
        font-weight: 600;
    }

    section.ready p {
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 500;
    }

    footer {
        margin-top: 0rem;
    }

    header.absensi-digital h1 {
        font-size: 32px;
    }

    section.list-absensi .list-absensi-item img {
        width: 44px;
        height: 44px;
    }

    section.list-absensi .list-absensi-item h2 {
        font-size: 16px;
    }

    .features-img-lg {
        display: none;
    }

    .features-img-sm {
        display: block;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    section.school-absensi {
        text-align: center;
    }

    section.school-absensi h1 {
        font-size: 20px;
    }

    .row-cols-5>* {
        width: 100%;
    }

    section.blog-populer .blog-populer-item img {
        display: none;
    }

    section.blog-populer .blog-populer-item h3 {
        font-size: 16px;
    }

    header.blog-detail h1 {
        font-size: 20px;
    }

    header.blog-detail h2 {
        font-size: 10px;
    }

    header.blog-detail p {
        font-size: 14px;
    }

    header.blog-detail {
        padding-top: 60px;
    }

    section.blog .blog-header h1 {
        font-size: 20px;
    }

    header.contact h2 {
        font-size: 24px;
    }

    header.contact p {
        font-size: 12px;
    }

    section.contact .card {
        padding: unset;
    }

    section.contact .contact-info p {
        font-size: 14px;
    }

    .faq .card {
        padding: unset;
    }

    .faq .faq-item {
        padding: 10px;
    }
}